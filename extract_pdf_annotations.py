import fitz  # PyMuPDF
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
import io

def extract_page_with_annotations(pdf_path, page_num, show_annotations=True):
    """
    从PDF中提取指定页面的图像，可选择是否显示注释
    
    Args:
        pdf_path (str): PDF文件路径
        page_num (int): 页面编号（从0开始）
        show_annotations (bool): 是否显示注释，True显示，False不显示
    
    Returns:
        PIL.Image: 提取的页面图像
    """
    # 打开PDF文件
    doc = fitz.open(pdf_path)
    
    # 检查页面编号是否有效
    if page_num >= len(doc):
        raise ValueError(f"页面编号 {page_num} 超出范围，PDF共有 {len(doc)} 页")
    
    # 获取指定页面
    page = doc[page_num]
    
    # 获取页面的pixmap，annots参数控制是否包含注释
    # annots=True 显示注释，annots=False 不显示注释
    pixmap = page.get_pixmap(annots=show_annotations)
    
    # 将pixmap转换为PIL Image
    img_data = pixmap.tobytes("ppm")
    img = Image.open(io.BytesIO(img_data))
    
    # 关闭文档
    doc.close()
    
    return img

def display_annotations_comparison(pdf_path, page_num):
    """
    显示同一页面带注释和不带注释的对比图
    
    Args:
        pdf_path (str): PDF文件路径
        page_num (int): 页面编号（从0开始）
    """
    # 提取带注释的图像
    img_with_annotations = extract_page_with_annotations(pdf_path, page_num, show_annotations=True)
    
    # 提取不带注释的图像
    img_without_annotations = extract_page_with_annotations(pdf_path, page_num, show_annotations=False)
    
    # 创建matplotlib图形
    fig, axes = plt.subplots(1, 2, figsize=(16, 10))
    
    # 显示带注释的图像
    axes[0].imshow(img_with_annotations)
    axes[0].set_title(f'第{page_num + 1}页 - 带注释 (annots=True)', fontsize=14, fontweight='bold')
    axes[0].axis('off')
    
    # 显示不带注释的图像
    axes[1].imshow(img_without_annotations)
    axes[1].set_title(f'第{page_num + 1}页 - 不带注释 (annots=False)', fontsize=14, fontweight='bold')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return img_with_annotations, img_without_annotations

def main():
    # PDF文件路径
    pdf_path = r"goodnotes\GoodNotes\考研材料\26王道数据结构.pdf"
    
    # 页面编号（第19页，索引为18）
    page_num = 18  # 第19页
    
    try:
        print(f"正在从 {pdf_path} 提取第 {page_num + 1} 页...")
        
        # 显示对比图
        img_with_annots, img_without_annots = display_annotations_comparison(pdf_path, page_num)
        
        print(f"成功提取第 {page_num + 1} 页图像")
        print(f"带注释图像尺寸: {img_with_annots.size}")
        print(f"不带注释图像尺寸: {img_without_annots.size}")
        
        # 可选：保存图像
        save_images = input("是否保存图像到文件？(y/n): ").lower().strip()
        if save_images == 'y':
            img_with_annots.save(f"page_{page_num + 1}_with_annotations.png")
            img_without_annots.save(f"page_{page_num + 1}_without_annotations.png")
            print("图像已保存")
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
